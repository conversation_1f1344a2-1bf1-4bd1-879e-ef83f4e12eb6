{"name": "akaunting/akaunting", "description": "Free and Online Accounting Software", "keywords": ["accounting", "finance", "laravel", "invoice", "expense"], "license": "GPL-3.0-or-later", "type": "project", "require": {"php": "^8.1", "ext-bcmath": "*", "ext-ctype": "*", "ext-curl": "*", "ext-dom": "*", "ext-fileinfo": "*", "ext-gd": "*", "ext-intl": "*", "ext-json": "*", "ext-mbstring": "*", "ext-openssl": "*", "ext-tokenizer": "*", "ext-xml": "*", "ext-zip": "*", "akaunting/laravel-apexcharts": "^3.0", "akaunting/laravel-firewall": "^2.0", "akaunting/laravel-language": "^1.0", "akaunting/laravel-menu": "^3.0", "akaunting/laravel-module": "^9.0", "akaunting/laravel-money": "^4.0", "akaunting/laravel-setting": "^1.2", "akaunting/laravel-sortable": "^2.0", "akaunting/laravel-version": "^1.0", "barryvdh/laravel-dompdf": "^2.0", "bkwld/cloner": "^3.4", "bugsnag/bugsnag-laravel": "^2.26", "doctrine/dbal": "^3.6", "guzzlehttp/guzzle": "^7.2", "intervention/image": "^2.7", "laravel/framework": "^10.0", "laravel/sanctum": "^3.2", "laravel/tinker": "^2.8", "livewire/livewire": "^3.0", "lorisleiva/laravel-search-string": "^1.3", "maatwebsite/excel": "^3.1", "omnipay/omnipay": "^3.0", "plank/laravel-mediable": "^5.11", "santigarcor/laratrust": "^7.0", "sentry/sentry-laravel": "^3.8", "spatie/laravel-model-caching": "^0.13"}, "require-dev": {"fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.0", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/", "Modules\\": "modules/"}, "files": ["app/Utilities/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}
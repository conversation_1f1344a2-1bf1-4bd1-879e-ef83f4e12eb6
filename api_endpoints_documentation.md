# API ENDPOINTS DOCUMENTATION - AKAUNTING

## Base URL
```
https://your-domain.com/api/
```

## Authentication
Akaunting uses Laravel Sanctum for API authentication.

### Login
```http
POST /api/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password"
}
```

Response:
```json
{
    "success": true,
    "data": {
        "token": "1|abc123...",
        "user": {
            "id": 1,
            "name": "Admin User",
            "email": "<EMAIL>"
        }
    }
}
```

### Headers for Authenticated Requests
```http
Authorization: Bearer {token}
Content-Type: application/json
Accept: application/json
```

## Core Endpoints

### Companies
```http
GET    /api/companies              # List companies
POST   /api/companies              # Create company
GET    /api/companies/{id}         # Show company
PUT    /api/companies/{id}         # Update company
DELETE /api/companies/{id}         # Delete company
GET    /api/companies/{id}/enable  # Enable company
GET    /api/companies/{id}/disable # Disable company
```

### Users
```http
GET    /api/users              # List users
POST   /api/users              # Create user
GET    /api/users/{id}         # Show user
PUT    /api/users/{id}         # Update user
DELETE /api/users/{id}         # Delete user
GET    /api/users/{id}/enable  # Enable user
GET    /api/users/{id}/disable # Disable user
```

### Accounts (Banking)
```http
GET    /api/accounts              # List accounts
POST   /api/accounts              # Create account
GET    /api/accounts/{id}         # Show account
PUT    /api/accounts/{id}         # Update account
DELETE /api/accounts/{id}         # Delete account
GET    /api/accounts/{id}/enable  # Enable account
GET    /api/accounts/{id}/disable # Disable account
```

### Transactions
```http
GET    /api/transactions          # List transactions
POST   /api/transactions          # Create transaction
GET    /api/transactions/{id}     # Show transaction
PUT    /api/transactions/{id}     # Update transaction
DELETE /api/transactions/{id}     # Delete transaction
```

### Documents (Invoices/Bills)
```http
GET    /api/documents                    # List documents
POST   /api/documents                    # Create document
GET    /api/documents/{id}               # Show document
PUT    /api/documents/{id}               # Update document
DELETE /api/documents/{id}               # Delete document
GET    /api/documents/{id}/received      # Mark as received
```

### Document Transactions
```http
GET    /api/documents/{id}/transactions     # List document transactions
POST   /api/documents/{id}/transactions     # Create document transaction
GET    /api/documents/{id}/transactions/{tid} # Show document transaction
PUT    /api/documents/{id}/transactions/{tid} # Update document transaction
DELETE /api/documents/{id}/transactions/{tid} # Delete document transaction
```

### Contacts (Customers/Vendors)
```http
GET    /api/contacts              # List contacts
POST   /api/contacts              # Create contact
GET    /api/contacts/{id}         # Show contact
PUT    /api/contacts/{id}         # Update contact
DELETE /api/contacts/{id}         # Delete contact
GET    /api/contacts/{id}/enable  # Enable contact
GET    /api/contacts/{id}/disable # Disable contact
```

### Items (Products/Services)
```http
GET    /api/items              # List items
POST   /api/items              # Create item
GET    /api/items/{id}         # Show item
PUT    /api/items/{id}         # Update item
DELETE /api/items/{id}         # Delete item
GET    /api/items/{id}/enable  # Enable item
GET    /api/items/{id}/disable # Disable item
```

### Categories
```http
GET    /api/categories              # List categories
POST   /api/categories              # Create category
GET    /api/categories/{id}         # Show category
PUT    /api/categories/{id}         # Update category
DELETE /api/categories/{id}         # Delete category
GET    /api/categories/{id}/enable  # Enable category
GET    /api/categories/{id}/disable # Disable category
```

### Currencies
```http
GET    /api/currencies              # List currencies
POST   /api/currencies              # Create currency
GET    /api/currencies/{id}         # Show currency
PUT    /api/currencies/{id}         # Update currency
DELETE /api/currencies/{id}         # Delete currency
GET    /api/currencies/{id}/enable  # Enable currency
GET    /api/currencies/{id}/disable # Disable currency
```

### Taxes
```http
GET    /api/taxes              # List taxes
POST   /api/taxes              # Create tax
GET    /api/taxes/{id}         # Show tax
PUT    /api/taxes/{id}         # Update tax
DELETE /api/taxes/{id}         # Delete tax
GET    /api/taxes/{id}/enable  # Enable tax
GET    /api/taxes/{id}/disable # Disable tax
```

### Settings
```http
GET    /api/settings          # List settings
POST   /api/settings          # Create setting
GET    /api/settings/{id}     # Show setting
PUT    /api/settings/{id}     # Update setting
DELETE /api/settings/{id}     # Delete setting
```

### Reports
```http
GET    /api/reports           # List available reports
GET    /api/reports/{type}    # Generate specific report
```

### Transfers
```http
GET    /api/transfers         # List transfers
POST   /api/transfers         # Create transfer
GET    /api/transfers/{id}    # Show transfer
PUT    /api/transfers/{id}    # Update transfer
DELETE /api/transfers/{id}    # Delete transfer
```

### Reconciliations
```http
GET    /api/reconciliations         # List reconciliations
POST   /api/reconciliations         # Create reconciliation
GET    /api/reconciliations/{id}    # Show reconciliation
PUT    /api/reconciliations/{id}    # Update reconciliation
DELETE /api/reconciliations/{id}    # Delete reconciliation
```

## Query Parameters

### Pagination
```http
GET /api/documents?page=1&limit=25
```

### Filtering
```http
GET /api/transactions?type=income&account_id=1
GET /api/documents?status=paid&contact_id=5
GET /api/contacts?type=customer&enabled=1
```

### Sorting
```http
GET /api/documents?sort=issued_at&order=desc
GET /api/transactions?sort=amount&order=asc
```

### Search
```http
GET /api/contacts?search=john
GET /api/items?search=product
```

## Request/Response Examples

### Create Invoice
```http
POST /api/documents
Content-Type: application/json

{
    "type": "invoice",
    "document_number": "INV-2024-001",
    "status": "draft",
    "issued_at": "2024-01-15",
    "due_at": "2024-02-15",
    "currency_code": "USD",
    "currency_rate": 1.0,
    "contact_id": 1,
    "contact_name": "John Doe",
    "contact_email": "<EMAIL>",
    "category_id": 1,
    "notes": "Thank you for your business",
    "items": [
        {
            "name": "Web Development",
            "quantity": 1,
            "price": 1000.00,
            "total": 1000.00
        }
    ]
}
```

Response:
```json
{
    "success": true,
    "data": {
        "id": 1,
        "type": "invoice",
        "document_number": "INV-2024-001",
        "status": "draft",
        "issued_at": "2024-01-15T00:00:00.000000Z",
        "due_at": "2024-02-15T00:00:00.000000Z",
        "amount": 1000.00,
        "currency_code": "USD",
        "contact": {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>"
        },
        "items": [
            {
                "id": 1,
                "name": "Web Development",
                "quantity": 1,
                "price": 1000.00,
                "total": 1000.00
            }
        ]
    }
}
```

### Create Transaction
```http
POST /api/transactions
Content-Type: application/json

{
    "type": "income",
    "account_id": 1,
    "paid_at": "2024-01-15",
    "amount": 1000.00,
    "currency_code": "USD",
    "currency_rate": 1.0,
    "contact_id": 1,
    "description": "Payment for Invoice INV-2024-001",
    "category_id": 1,
    "payment_method": "bank_transfer",
    "reference": "TXN-001"
}
```

## Error Responses

### Validation Error (422)
```json
{
    "success": false,
    "message": "The given data was invalid.",
    "errors": {
        "email": [
            "The email field is required."
        ],
        "amount": [
            "The amount must be greater than 0."
        ]
    }
}
```

### Unauthorized (401)
```json
{
    "success": false,
    "message": "Unauthenticated."
}
```

### Forbidden (403)
```json
{
    "success": false,
    "message": "This action is unauthorized."
}
```

### Not Found (404)
```json
{
    "success": false,
    "message": "Resource not found."
}
```

## Rate Limiting
- 60 requests per minute per IP address
- 1000 requests per hour per authenticated user

## Middleware Applied
- `auth:sanctum` - Authentication required
- `company` - Company context required
- `permission` - Permission checking
- `throttle` - Rate limiting
- `date.format` - Date formatting
- `money` - Money formatting
- `dropzone` - File upload handling

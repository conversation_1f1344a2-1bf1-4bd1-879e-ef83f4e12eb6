{"name": "akaunting", "version": "3.0.0", "description": "Free and Online Accounting Software", "main": "webpack.mix.js", "scripts": {"dev": "npm run development", "development": "mix", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000", "hot": "mix watch --hot", "prod": "npm run production", "production": "mix --production", "test": "jest"}, "devDependencies": {"@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "@popperjs/core": "^2.11.8", "autoprefixer": "^10.4.14", "axios": "^1.4.0", "bootstrap": "^5.3.0", "cross-env": "^7.0.3", "jest": "^29.5.0", "laravel-mix": "^6.0.49", "lodash": "^4.17.21", "postcss": "^8.4.24", "resolve-url-loader": "^5.0.0", "sass": "^1.63.6", "sass-loader": "^13.3.2", "tailwindcss": "^3.3.6", "vue-loader": "^17.2.2", "vue-template-compiler": "^2.7.14"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.4.0", "@tailwindcss/forms": "^0.5.3", "@tailwindcss/typography": "^0.5.9", "alpinejs": "^3.12.3", "apexcharts": "^3.41.0", "element-ui": "^2.15.13", "flowbite": "^1.7.0", "quill": "^1.3.7", "sortablejs": "^1.15.0", "vue": "^2.7.14", "vue-apexcharts": "^1.6.2", "vue-color": "^2.8.1", "vue-multiselect": "^2.1.6", "vue-router": "^3.6.5", "vue2-editor": "^2.10.3", "vuedraggable": "^2.24.3", "vuex": "^3.6.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"], "jest": {"testEnvironment": "jsdom", "moduleFileExtensions": ["js", "vue"], "transform": {"^.+\\.js$": "babel-jest", "^.+\\.vue$": "@vue/vue2-jest"}, "moduleNameMapping": {"^@/(.*)$": "<rootDir>/resources/assets/js/$1"}}}
# Guía de Desarrollo - Akaunting Clone

## Tabla de Contenidos
1. [Configuración Inicial](#configuración-inicial)
2. [Estructura del Proyecto](#estructura-del-proyecto)
3. [Base de Datos](#base-de-datos)
4. [Backend Development](#backend-development)
5. [Frontend Development](#frontend-development)
6. [Testing](#testing)
7. [Deployment](#deployment)
8. [<PERSON><PERSON><PERSON><PERSON> y Extensiones](#módulos-y-extensiones)

---

## Configuración Inicial

### Requisitos del Sistema
- PHP 8.1+
- Node.js 16+
- Composer 2.0+
- MySQL 8.0+ / PostgreSQL 13+ / SQLite 3.8+
- Redis (opcional, para cache y queues)

### Stack Tecnológico
```
Backend:
- Laravel 10.x
- PHP 8.1+
- MySQL/PostgreSQL
- Redis

Frontend:
- Vue.js 3.x
- Tailwind CSS 3.x
- Laravel Mix
- Axios

Tools:
- PHPUnit (Testing)
- Laravel Sanctum (API Auth)
- <PERSON><PERSON> Mix (Asset Compilation)
```

### Instalación Paso a Paso

#### 1. Crear Proyecto <PERSON>
```bash
composer create-project laravel/laravel akaunting-clone
cd akaunting-clone
```

#### 2. Instalar Dependencias Backend
```bash
composer require laravel/sanctum
composer require maatwebsite/excel
composer require barryvdh/laravel-dompdf
composer require spatie/laravel-permission
composer require akaunting/laravel-module
```

#### 3. Instalar Dependencias Frontend
```bash
npm install
npm install vue@next @vue/compiler-sfc
npm install tailwindcss @tailwindcss/forms
npm install laravel-mix-tailwind
```

#### 4. Configurar Entorno
```bash
cp .env.example .env
php artisan key:generate
```

---

## Estructura del Proyecto

### Arquitectura de Carpetas
```
akaunting-clone/
├── app/
│   ├── Console/
│   │   ├── Commands/
│   │   │   ├── InstallCommand.php
│   │   │   └── SampleDataCommand.php
│   │   └── Kernel.php
│   ├── Http/
│   │   ├── Controllers/
│   │   │   ├── Api/
│   │   │   │   ├── Auth/
│   │   │   │   ├── Banking/
│   │   │   │   ├── Common/
│   │   │   │   ├── Purchases/
│   │   │   │   └── Sales/
│   │   │   └── Web/
│   │   ├── Middleware/
│   │   ├── Requests/
│   │   └── Resources/
│   ├── Models/
│   │   ├── Auth/
│   │   ├── Banking/
│   │   ├── Common/
│   │   ├── Purchases/
│   │   └── Sales/
│   ├── Services/
│   ├── Traits/
│   └── Utilities/
├── config/
├── database/
│   ├── factories/
│   ├── migrations/
│   └── seeders/
├── modules/
├── resources/
│   ├── assets/
│   │   ├── js/
│   │   │   ├── components/
│   │   │   ├── pages/
│   │   │   ├── store/
│   │   │   └── utils/
│   │   └── sass/
│   ├── lang/
│   └── views/
├── routes/
│   ├── api.php
│   ├── web.php
│   └── modules.php
└── tests/
    ├── Feature/
    └── Unit/
```

---

## Base de Datos

### Esquema Principal

#### 1. Tabla Companies
```sql
CREATE TABLE companies (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    domain VARCHAR(255) NULL,
    logo VARCHAR(255) NULL,
    address TEXT NULL,
    phone VARCHAR(255) NULL,
    website VARCHAR(255) NULL,
    tax_number VARCHAR(255) NULL,
    currency_code VARCHAR(3) DEFAULT 'USD',
    locale VARCHAR(6) DEFAULT 'en-GB',
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_companies_enabled (enabled),
    UNIQUE KEY unique_companies_email (email)
);
```

#### 2. Tabla Users
```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    locale VARCHAR(6) DEFAULT 'en-GB',
    landing_page VARCHAR(255) DEFAULT 'dashboard',
    enabled BOOLEAN DEFAULT TRUE,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    INDEX idx_users_company (company_id),
    INDEX idx_users_enabled (enabled),
    UNIQUE KEY unique_users_email (email)
);
```

#### 3. Tabla Accounts
```sql
CREATE TABLE accounts (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    number VARCHAR(255) NOT NULL,
    currency_code VARCHAR(3) NOT NULL,
    opening_balance DECIMAL(15,4) DEFAULT 0.0000,
    current_balance DECIMAL(15,4) DEFAULT 0.0000,
    bank_name VARCHAR(255) NULL,
    bank_phone VARCHAR(255) NULL,
    bank_address TEXT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    INDEX idx_accounts_company (company_id),
    INDEX idx_accounts_enabled (enabled)
);
```

#### 4. Tabla Customers
```sql
CREATE TABLE customers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NULL,
    phone VARCHAR(255) NULL,
    address TEXT NULL,
    website VARCHAR(255) NULL,
    tax_number VARCHAR(255) NULL,
    currency_code VARCHAR(3) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    INDEX idx_customers_company (company_id),
    INDEX idx_customers_enabled (enabled)
);
```

#### 5. Tabla Invoices
```sql
CREATE TABLE invoices (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    customer_id BIGINT UNSIGNED NOT NULL,
    invoice_number VARCHAR(255) NOT NULL,
    order_number VARCHAR(255) NULL,
    status VARCHAR(255) DEFAULT 'draft',
    invoiced_at DATE NOT NULL,
    due_at DATE NOT NULL,
    amount DECIMAL(15,4) NOT NULL,
    currency_code VARCHAR(3) NOT NULL,
    currency_rate DECIMAL(15,8) DEFAULT 1.00000000,
    notes TEXT NULL,
    footer TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    INDEX idx_invoices_company (company_id),
    INDEX idx_invoices_customer (customer_id),
    INDEX idx_invoices_status (status),
    UNIQUE KEY unique_invoices_number (company_id, invoice_number)
);
```

### Migraciones de Desarrollo

#### Comando para crear migraciones
```bash
# Crear migraciones principales
php artisan make:migration create_companies_table
php artisan make:migration create_users_table
php artisan make:migration create_accounts_table
php artisan make:migration create_customers_table
php artisan make:migration create_vendors_table
php artisan make:migration create_items_table
php artisan make:migration create_invoices_table
php artisan make:migration create_invoice_items_table
php artisan make:migration create_bills_table
php artisan make:migration create_bill_items_table
php artisan make:migration create_transactions_table
php artisan make:migration create_categories_table
php artisan make:migration create_taxes_table
php artisan make:migration create_currencies_table

# Ejecutar migraciones
php artisan migrate
```

---

## Backend Development

### Modelos Base

#### 1. Company Model
```php
<?php

namespace App\Models\Common;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Company extends Model
{
    protected $fillable = [
        'name', 'email', 'domain', 'logo', 'address', 
        'phone', 'website', 'tax_number', 'currency_code', 
        'locale', 'enabled'
    ];

    protected $casts = [
        'enabled' => 'boolean',
    ];

    // Relationships
    public function users(): HasMany
    {
        return $this->hasMany(User::class);
    }

    public function accounts(): HasMany
    {
        return $this->hasMany(Account::class);
    }

    public function customers(): HasMany
    {
        return $this->hasMany(Customer::class);
    }

    public function invoices(): HasMany
    {
        return $this->hasMany(Invoice::class);
    }
}
```

#### 2. User Model
```php
<?php

namespace App\Models\Auth;

use Illuminate\Foundation\Auth\User as Authenticatable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    use HasApiTokens, HasRoles;

    protected $fillable = [
        'company_id', 'name', 'email', 'password', 
        'locale', 'landing_page', 'enabled'
    ];

    protected $hidden = [
        'password', 'remember_token',
    ];

    protected $casts = [
        'email_verified_at' => 'datetime',
        'enabled' => 'boolean',
    ];

    public function company()
    {
        return $this->belongsTo(Company::class);
    }
}
```

### Controladores API

#### 1. Base API Controller
```php
<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;

abstract class BaseController extends Controller
{
    protected function successResponse($data, $message = null, $code = 200): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ], $code);
    }

    protected function errorResponse($message, $code = 400): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message
        ], $code);
    }
}
```

#### 2. Companies Controller
```php
<?php

namespace App\Http\Controllers\Api\Common;

use App\Http\Controllers\Api\BaseController;
use App\Models\Common\Company;
use App\Http\Requests\Company\StoreRequest;
use App\Http\Requests\Company\UpdateRequest;

class CompaniesController extends BaseController
{
    public function index()
    {
        $companies = Company::with(['users', 'accounts'])
            ->where('enabled', true)
            ->paginate(15);

        return $this->successResponse($companies);
    }

    public function store(StoreRequest $request)
    {
        $company = Company::create($request->validated());
        
        return $this->successResponse($company, 'Company created successfully', 201);
    }

    public function show(Company $company)
    {
        return $this->successResponse($company->load(['users', 'accounts']));
    }

    public function update(UpdateRequest $request, Company $company)
    {
        $company->update($request->validated());
        
        return $this->successResponse($company, 'Company updated successfully');
    }

    public function destroy(Company $company)
    {
        $company->delete();
        
        return $this->successResponse(null, 'Company deleted successfully', 204);
    }
}
```

### Request Validation

#### Company Store Request
```php
<?php

namespace App\Http\Requests\Company;

use Illuminate\Foundation\Http\FormRequest;

class StoreRequest extends FormRequest
{
    public function authorize()
    {
        return true;
    }

    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:companies',
            'domain' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'phone' => 'nullable|string|max:255',
            'website' => 'nullable|url|max:255',
            'tax_number' => 'nullable|string|max:255',
            'currency_code' => 'required|string|size:3',
            'locale' => 'required|string|size:5',
        ];
    }
}
```

---

## Frontend Development

### Vue.js Setup

#### 1. Main App Component
```javascript
// resources/assets/js/app.js
import { createApp } from 'vue';
import router from './router';
import store from './store';
import axios from 'axios';

// Configure Axios
axios.defaults.baseURL = '/api';
axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

// Get CSRF token
const token = document.head.querySelector('meta[name="csrf-token"]');
if (token) {
    axios.defaults.headers.common['X-CSRF-TOKEN'] = token.content;
}

const app = createApp({});

// Global components
import Dashboard from './pages/Dashboard.vue';
import CompanyList from './components/Companies/CompanyList.vue';
import InvoiceList from './components/Invoices/InvoiceList.vue';

app.component('dashboard', Dashboard);
app.component('company-list', CompanyList);
app.component('invoice-list', InvoiceList);

app.use(router);
app.use(store);
app.mount('#app');
```

#### 2. Vue Router Configuration
```javascript
// resources/assets/js/router/index.js
import { createRouter, createWebHistory } from 'vue-router';

const routes = [
    {
        path: '/',
        name: 'Dashboard',
        component: () => import('../pages/Dashboard.vue')
    },
    {
        path: '/companies',
        name: 'Companies',
        component: () => import('../pages/Companies/Index.vue')
    },
    {
        path: '/companies/create',
        name: 'CompanyCreate',
        component: () => import('../pages/Companies/Create.vue')
    },
    {
        path: '/invoices',
        name: 'Invoices',
        component: () => import('../pages/Invoices/Index.vue')
    },
    {
        path: '/invoices/create',
        name: 'InvoiceCreate',
        component: () => import('../pages/Invoices/Create.vue')
    }
];

const router = createRouter({
    history: createWebHistory(),
    routes
});

export default router;
```

#### 3. Vuex Store
```javascript
// resources/assets/js/store/index.js
import { createStore } from 'vuex';
import companies from './modules/companies';
import invoices from './modules/invoices';
import auth from './modules/auth';

export default createStore({
    modules: {
        companies,
        invoices,
        auth
    }
});
```

### Componentes Base

#### 1. Company List Component
```vue
<!-- resources/assets/js/components/Companies/CompanyList.vue -->
<template>
  <div class="company-list">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Companies</h1>
      <router-link 
        to="/companies/create" 
        class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
      >
        Add Company
      </router-link>
    </div>

    <div class="bg-white shadow overflow-hidden sm:rounded-md">
      <ul class="divide-y divide-gray-200">
        <li v-for="company in companies" :key="company.id" class="px-6 py-4">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-medium text-gray-900">{{ company.name }}</h3>
              <p class="text-sm text-gray-500">{{ company.email }}</p>
            </div>
            <div class="flex space-x-2">
              <button 
                @click="editCompany(company)" 
                class="text-blue-600 hover:text-blue-900"
              >
                Edit
              </button>
              <button 
                @click="deleteCompany(company)" 
                class="text-red-600 hover:text-red-900"
              >
                Delete
              </button>
            </div>
          </div>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions } from 'vuex';

export default {
  name: 'CompanyList',
  computed: {
    ...mapState('companies', ['companies', 'loading'])
  },
  mounted() {
    this.fetchCompanies();
  },
  methods: {
    ...mapActions('companies', ['fetchCompanies', 'deleteCompany']),
    editCompany(company) {
      this.$router.push(`/companies/${company.id}/edit`);
    },
    async deleteCompany(company) {
      if (confirm('Are you sure you want to delete this company?')) {
        await this.deleteCompany(company.id);
        this.fetchCompanies();
      }
    }
  }
};
</script>
```

---

## Testing

### PHPUnit Configuration

#### 1. Feature Tests
```php
<?php
// tests/Feature/CompanyTest.php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Common\Company;
use App\Models\Auth\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CompanyTest extends TestCase
{
    use RefreshDatabase;

    public function test_can_create_company()
    {
        $user = User::factory()->create();
        
        $response = $this->actingAs($user, 'sanctum')
            ->postJson('/api/companies', [
                'name' => 'Test Company',
                'email' => '<EMAIL>',
                'currency_code' => 'USD',
                'locale' => 'en-GB'
            ]);

        $response->assertStatus(201)
            ->assertJson([
                'success' => true,
                'data' => [
                    'name' => 'Test Company',
                    'email' => '<EMAIL>'
                ]
            ]);
    }

    public function test_can_list_companies()
    {
        $user = User::factory()->create();
        Company::factory()->count(3)->create();

        $response = $this->actingAs($user, 'sanctum')
            ->getJson('/api/companies');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'data' => [
                        '*' => ['id', 'name', 'email', 'enabled']
                    ]
                ]
            ]);
    }
}
```

#### 2. Unit Tests
```php
<?php
// tests/Unit/CompanyModelTest.php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\Common\Company;
use App\Models\Auth\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

class CompanyModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_company_has_users_relationship()
    {
        $company = Company::factory()->create();
        $user = User::factory()->create(['company_id' => $company->id]);

        $this->assertTrue($company->users->contains($user));
    }

    public function test_company_enabled_scope()
    {
        Company::factory()->create(['enabled' => true]);
        Company::factory()->create(['enabled' => false]);

        $enabledCompanies = Company::where('enabled', true)->get();
        
        $this->assertCount(1, $enabledCompanies);
    }
}
```

---

## Deployment

### Production Configuration

#### 1. Environment Variables
```bash
# .env.production
APP_NAME="Akaunting Clone"
APP_ENV=production
APP_KEY=base64:your-app-key-here
APP_DEBUG=false
APP_URL=https://your-domain.com

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=akaunting_production
DB_USERNAME=akaunting_user
DB_PASSWORD=secure_password

CACHE_DRIVER=redis
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.mailtrap.io
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"
```

#### 2. Deployment Script
```bash
#!/bin/bash
# deploy.sh

echo "Starting deployment..."

# Pull latest code
git pull origin main

# Install/update dependencies
composer install --no-dev --optimize-autoloader
npm ci
npm run production

# Run migrations
php artisan migrate --force

# Clear and cache config
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Restart queue workers
php artisan queue:restart

# Set permissions
chmod -R 755 storage bootstrap/cache
chown -R www-data:www-data storage bootstrap/cache

echo "Deployment completed!"
```

---

## Módulos y Extensiones

### Sistema de Módulos

#### 1. Estructura de Módulo
```
modules/
├── PayPal/
│   ├── Config/
│   ├── Console/
│   ├── Database/
│   ├── Http/
│   │   ├── Controllers/
│   │   ├── Middleware/
│   │   └── Requests/
│   ├── Models/
│   ├── Providers/
│   ├── Resources/
│   │   ├── assets/
│   │   ├── lang/
│   │   └── views/
│   ├── Routes/
│   ├── Tests/
│   ├── composer.json
│   └── module.json
```

#### 2. Module Service Provider
```php
<?php
// modules/PayPal/Providers/PayPalServiceProvider.php

namespace Modules\PayPal\Providers;

use Illuminate\Support\ServiceProvider;

class PayPalServiceProvider extends ServiceProvider
{
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->loadMigrationsFrom(module_path('PayPal', 'Database/Migrations'));
    }

    public function register()
    {
        $this->app->register(RouteServiceProvider::class);
    }

    protected function registerConfig()
    {
        $this->publishes([
            module_path('PayPal', 'Config/config.php') => config_path('paypal.php'),
        ], 'config');
        
        $this->mergeConfigFrom(
            module_path('PayPal', 'Config/config.php'), 'paypal'
        );
    }
}
```

---

## Comandos de Desarrollo

### Comandos Artisan Personalizados

```bash
# Instalar aplicación
php artisan install --db-name=akaunting --admin-email=<EMAIL>

# Generar datos de prueba
php artisan sample-data:seed

# Crear módulo
php artisan module:make PayPal

# Publicar módulo
php artisan module:publish PayPal

# Ejecutar tests
php artisan test --parallel

# Compilar assets
npm run dev
npm run watch
npm run production
```

---

## Checklist de Desarrollo

### Backend
- [ ] Configurar Laravel con dependencias
- [ ] Crear migraciones de base de datos
- [ ] Implementar modelos Eloquent
- [ ] Desarrollar controladores API
- [ ] Configurar autenticación Sanctum
- [ ] Implementar validaciones de request
- [ ] Crear seeders y factories
- [ ] Configurar permisos y roles

### Frontend
- [ ] Configurar Vue.js 3
- [ ] Implementar router y store
- [ ] Crear componentes base
- [ ] Desarrollar páginas principales
- [ ] Integrar con API backend
- [ ] Implementar validaciones frontend
- [ ] Configurar Tailwind CSS
- [ ] Optimizar assets con Mix

### Testing
- [ ] Configurar PHPUnit
- [ ] Escribir tests unitarios
- [ ] Crear tests de feature
- [ ] Implementar tests de API
- [ ] Configurar CI/CD pipeline
- [ ] Tests de integración frontend

### Deployment
- [ ] Configurar servidor producción
- [ ] Optimizar configuración Laravel
- [ ] Configurar SSL y dominio
- [ ] Implementar backup automático
- [ ] Configurar monitoreo
- [ ] Documentar proceso deployment

---

## Recursos Adicionales

### Documentación
- [Laravel Documentation](https://laravel.com/docs)
- [Vue.js Guide](https://vuejs.org/guide/)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [PHPUnit Documentation](https://phpunit.de/documentation.html)

### Herramientas Recomendadas
- **IDE**: PhpStorm, VS Code
- **Database**: MySQL Workbench, phpMyAdmin
- **API Testing**: Postman, Insomnia
- **Version Control**: Git, GitHub/GitLab
- **Deployment**: Laravel Forge, DigitalOcean

---

*Esta guía proporciona una base sólida para desarrollar un clon completo de Akaunting. Sigue los pasos en orden y adapta según las necesidades específicas del proyecto.*
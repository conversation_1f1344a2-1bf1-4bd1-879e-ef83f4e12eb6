-- Akaunting Database Schema
-- Complete database structure for replication

-- Companies table (Multi-tenant support)
CREATE TABLE companies (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    domain VARCHAR(255) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    created_from VA<PERSON><PERSON><PERSON>(255) NULL,
    created_by INT UNSIGNED NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_companies_domain (domain),
    INDEX idx_companies_enabled (enabled)
);

-- Users table
CREATE TABLE users (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    remember_token VARCHAR(100) NULL,
    last_logged_in_at TIMESTAMP NULL,
    locale VARCHAR(10) DEFAULT 'en-GB',
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    <PERSON>IQUE KEY unique_email_deleted (email, deleted_at)
);

-- User-Company relationship (Many-to-Many)
CREATE TABLE user_companies (
    user_id INT UNSIGNED NOT NULL,
    company_id INT UNSIGNED NOT NULL,
    PRIMARY KEY (user_id, company_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);

-- Roles and Permissions (Laratrust)
CREATE TABLE roles (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);

CREATE TABLE permissions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    display_name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);

CREATE TABLE role_permissions (
    role_id INT UNSIGNED NOT NULL,
    permission_id INT UNSIGNED NOT NULL,
    PRIMARY KEY (role_id, permission_id),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE
);

CREATE TABLE user_roles (
    user_id INT UNSIGNED NOT NULL,
    role_id INT UNSIGNED NOT NULL,
    user_type VARCHAR(255) NOT NULL,
    PRIMARY KEY (user_id, role_id, user_type),
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE CASCADE
);

-- Categories table
CREATE TABLE categories (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(255) NOT NULL, -- income, expense, item, other
    color VARCHAR(7) NOT NULL DEFAULT '#3B82F6',
    parent_id INT UNSIGNED NULL,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_categories_company (company_id),
    INDEX idx_categories_type (type),
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Currencies table
CREATE TABLE currencies (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    code VARCHAR(3) NOT NULL,
    rate DECIMAL(15,8) NOT NULL DEFAULT 1.00000000,
    precision INT DEFAULT 2,
    symbol VARCHAR(10) NULL,
    symbol_first BOOLEAN DEFAULT TRUE,
    decimal_mark VARCHAR(1) DEFAULT '.',
    thousands_separator VARCHAR(1) DEFAULT ',',
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_currencies_company (company_id),
    UNIQUE KEY unique_company_code_deleted (company_id, code, deleted_at)
);

-- Taxes table
CREATE TABLE taxes (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    rate DECIMAL(15,4) NOT NULL,
    type VARCHAR(255) DEFAULT 'normal', -- normal, compound, withholding
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_taxes_company (company_id)
);

-- Accounts table (Bank accounts, cash accounts, etc.)
CREATE TABLE accounts (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    type VARCHAR(255) DEFAULT 'bank', -- bank, cash, credit_card
    name VARCHAR(255) NOT NULL,
    number VARCHAR(255) NOT NULL,
    currency_code VARCHAR(3) NOT NULL,
    opening_balance DECIMAL(15,4) DEFAULT 0.0000,
    bank_name VARCHAR(255) NULL,
    bank_phone VARCHAR(255) NULL,
    bank_address TEXT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_accounts_company (company_id),
    INDEX idx_accounts_currency (currency_code)
);

-- Contacts table (Customers and Vendors)
CREATE TABLE contacts (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    type VARCHAR(255) NOT NULL, -- customer, vendor
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NULL,
    tax_number VARCHAR(255) NULL,
    phone VARCHAR(255) NULL,
    address TEXT NULL,
    website VARCHAR(255) NULL,
    currency_code VARCHAR(3) NOT NULL,
    enabled BOOLEAN DEFAULT TRUE,
    reference VARCHAR(255) NULL,
    created_from VARCHAR(255) NULL,
    created_by INT UNSIGNED NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_contacts_company (company_id),
    INDEX idx_contacts_type (type),
    INDEX idx_contacts_email (email)
);

-- Items table (Products and Services)
CREATE TABLE items (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    type VARCHAR(255) DEFAULT 'product', -- product, service
    name VARCHAR(255) NOT NULL,
    sku VARCHAR(255) NOT NULL,
    description TEXT NULL,
    sale_price DECIMAL(15,4) NULL,
    purchase_price DECIMAL(15,4) NULL,
    category_id INT UNSIGNED NULL,
    enabled BOOLEAN DEFAULT TRUE,
    created_from VARCHAR(255) NULL,
    created_by INT UNSIGNED NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_items_company (company_id),
    INDEX idx_items_category (category_id),
    UNIQUE KEY unique_company_sku_deleted (company_id, sku, deleted_at),
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Documents table (Invoices, Bills, etc.)
CREATE TABLE documents (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    type VARCHAR(255) NOT NULL, -- invoice, bill, invoice-recurring, bill-recurring
    document_number VARCHAR(255) NOT NULL,
    order_number VARCHAR(255) NULL,
    status VARCHAR(255) NOT NULL, -- draft, sent, viewed, approved, partial, paid, overdue, unpaid, cancelled
    issued_at TIMESTAMP NOT NULL,
    due_at TIMESTAMP NOT NULL,
    amount DECIMAL(15,4) NOT NULL,
    currency_code VARCHAR(3) NOT NULL,
    currency_rate DECIMAL(15,8) NOT NULL,
    discount_type VARCHAR(255) DEFAULT 'percentage', -- percentage, fixed
    discount_rate DECIMAL(15,4) DEFAULT 0.0000,
    category_id INT UNSIGNED DEFAULT 1,
    contact_id INT UNSIGNED NOT NULL,
    contact_name VARCHAR(255) NOT NULL,
    contact_email VARCHAR(255) NULL,
    contact_tax_number VARCHAR(255) NULL,
    contact_phone VARCHAR(255) NULL,
    contact_address TEXT NULL,
    contact_country VARCHAR(255) NULL,
    contact_state VARCHAR(255) NULL,
    contact_zip_code VARCHAR(255) NULL,
    contact_city VARCHAR(255) NULL,
    title VARCHAR(255) NULL,
    subheading VARCHAR(255) NULL,
    notes TEXT NULL,
    footer TEXT NULL,
    template VARCHAR(255) DEFAULT 'default',
    color VARCHAR(7) DEFAULT '#3B82F6',
    parent_id INT UNSIGNED DEFAULT 0,
    created_from VARCHAR(255) NULL,
    created_by INT UNSIGNED NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_documents_company (company_id),
    INDEX idx_documents_type (type),
    INDEX idx_documents_status (status),
    INDEX idx_documents_contact (contact_id),
    UNIQUE KEY unique_company_number_deleted (company_id, document_number, deleted_at),
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE RESTRICT,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL
);

-- Document Items table
CREATE TABLE document_items (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    document_id INT UNSIGNED NOT NULL,
    item_id INT UNSIGNED NULL,
    name VARCHAR(255) NOT NULL,
    sku VARCHAR(255) NULL,
    quantity DECIMAL(7,2) NOT NULL,
    price DECIMAL(15,4) NOT NULL,
    total DECIMAL(15,4) NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_document_items_company (company_id),
    INDEX idx_document_items_document (document_id),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (item_id) REFERENCES items(id) ON DELETE SET NULL
);

-- Document Item Taxes table
CREATE TABLE document_item_taxes (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    document_id INT UNSIGNED NOT NULL,
    document_item_id INT UNSIGNED NOT NULL,
    tax_id INT UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    amount DECIMAL(15,4) DEFAULT 0.0000,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_document_item_taxes_company (company_id),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE,
    FOREIGN KEY (document_item_id) REFERENCES document_items(id) ON DELETE CASCADE,
    FOREIGN KEY (tax_id) REFERENCES taxes(id) ON DELETE CASCADE
);

-- Document Totals table
CREATE TABLE document_totals (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    document_id INT UNSIGNED NOT NULL,
    code VARCHAR(255) NULL, -- sub_total, tax, discount, total
    name VARCHAR(255) NOT NULL,
    amount DECIMAL(15,4) NOT NULL,
    sort_order INT NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_document_totals_company (company_id),
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE CASCADE
);

-- Transactions table (Payments, Expenses)
CREATE TABLE transactions (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    type VARCHAR(255) NOT NULL, -- income, expense, income-transfer, expense-transfer, income-split, expense-split
    number VARCHAR(255) NULL,
    account_id INT UNSIGNED NOT NULL,
    paid_at TIMESTAMP NOT NULL,
    amount DECIMAL(15,4) NOT NULL,
    currency_code VARCHAR(3) NOT NULL,
    currency_rate DECIMAL(15,8) NOT NULL,
    document_id INT UNSIGNED NULL,
    contact_id INT UNSIGNED NULL,
    description TEXT NOT NULL,
    category_id INT UNSIGNED NOT NULL,
    payment_method VARCHAR(255) NOT NULL,
    reference VARCHAR(255) NULL,
    parent_id INT UNSIGNED DEFAULT 0,
    split_id INT UNSIGNED NULL,
    created_from VARCHAR(255) NULL,
    created_by INT UNSIGNED NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_transactions_company (company_id),
    INDEX idx_transactions_type (type),
    INDEX idx_transactions_account (account_id),
    INDEX idx_transactions_document (document_id),
    INDEX idx_transactions_contact (contact_id),
    FOREIGN KEY (account_id) REFERENCES accounts(id) ON DELETE RESTRICT,
    FOREIGN KEY (document_id) REFERENCES documents(id) ON DELETE SET NULL,
    FOREIGN KEY (contact_id) REFERENCES contacts(id) ON DELETE SET NULL,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE RESTRICT,
    FOREIGN KEY (split_id) REFERENCES transactions(id) ON DELETE SET NULL
);

-- Settings table
CREATE TABLE settings (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    `key` VARCHAR(255) NOT NULL,
    value TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_settings_company (company_id),
    UNIQUE KEY unique_company_key (company_id, `key`)
);

-- Recurring table (for recurring invoices, bills, transactions)
CREATE TABLE recurring (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id INT UNSIGNED NOT NULL,
    recurable_type VARCHAR(255) NOT NULL,
    recurable_id INT UNSIGNED NOT NULL,
    frequency VARCHAR(255) NOT NULL, -- daily, weekly, monthly, yearly
    `interval` INT DEFAULT 1,
    started_at TIMESTAMP NOT NULL,
    status VARCHAR(255) DEFAULT 'active', -- active, completed, cancelled
    limit_by VARCHAR(255) DEFAULT 'count', -- count, date
    limit_count INT NULL,
    limit_date TIMESTAMP NULL,
    auto_send BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    INDEX idx_recurring_company (company_id),
    INDEX idx_recurring_recurable (recurable_type, recurable_id)
);
